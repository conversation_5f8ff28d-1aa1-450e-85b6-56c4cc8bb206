// SCRAPER MULTI-SITES - Tous les sites immobiliers industriels
const http = require('http');
const https = require('https');
const { URL } = require('url');

const PORT = 3000;

// Critères de recherche selon ton cahier des charges - ENTREPÔTS
const SEARCH_CRITERIA = {
  minPrice: 800000,
  maxPrice: 1400000, // Étendu à 1,4M€
  minBuildingArea: 400,
  idealBuildingArea: 1100,
  minLandArea: 3500,
  cities: ['Lyon', 'Bordeaux'],
  maxDistanceToHighway: 15,
  minCeilingHeight: 5.5,
  idealCeilingHeight: 6.5,
  propertyTypes: ['entrepot', 'entrepôt', 'warehouse', 'local industriel', 'local d\'activité', 'hangar']
};

// Base de données en mémoire pour stocker les annonces scrapées
let allProperties = [];
let scrapingStatus = { isRunning: false, currentStep: '', progress: 0 };

// Configuration des sites à scraper - FOCUS ENTREPÔTS
const SITES_CONFIG = {
  leboncoin: {
    name: 'Le<PERSON>on<PERSON>oin',
    baseUrl: 'https://www.leboncoin.fr',
    searchUrls: [
      'https://www.leboncoin.fr/recherche?category=9&locations=Lyon_69000__45.764043_4.835659_15000&price=800000-1400000&text=entrepot',
      'https://www.leboncoin.fr/recherche?category=9&locations=Bordeaux_33000__44.837789_-0.57918_15000&price=800000-1400000&text=entrepot'
    ],
    selectors: {
      propertyLinks: 'a[href*="/ventes_immobilieres/"]',
      title: 'h1, [data-qa-id="adview_title"]',
      price: '[data-qa-id="adview_price"], .price',
      description: '[data-qa-id="adview_description_container"], .description',
      area: 'text'
    }
  },

  bienici: {
    name: 'Bien\'ici',
    baseUrl: 'https://www.bienici.com',
    searchUrls: [
      'https://www.bienici.com/recherche/achat/lyon-69000?prix-max=1400000&prix-min=800000&types=entrepot,local-industriel',
      'https://www.bienici.com/recherche/achat/bordeaux-33000?prix-max=1400000&prix-min=800000&types=entrepot,local-industriel'
    ],
    selectors: {
      propertyLinks: 'a[href*="/annonce/"]',
      title: 'h1, .ad-title',
      price: '.price, .ad-price',
      description: '.description, .ad-description',
      area: 'text'
    }
  },

  seloger: {
    name: 'SeLoger',
    baseUrl: 'https://www.seloger.com',
    searchUrls: [
      'https://www.seloger.com/list.htm?types=11,12&places=[{%22inseeCodes%22:[69123]}]&price=800000/1400000&surface=400/UNLIMITED',
      'https://www.seloger.com/list.htm?types=11,12&places=[{%22inseeCodes%22:[33063]}]&price=800000/1400000&surface=400/UNLIMITED'
    ],
    selectors: {
      propertyLinks: 'a[href*="/annonces/"]',
      title: 'h1, .detail-title',
      price: '.price, .detail-price',
      description: '.description, .detail-description',
      area: 'text'
    }
  },

  geolocaux: {
    name: 'Geolocaux',
    baseUrl: 'https://www.geolocaux.com',
    searchUrls: [
      'https://www.geolocaux.com/vente/entrepot/lyon?prix_min=800000&prix_max=1400000&surface_min=400',
      'https://www.geolocaux.com/vente/entrepot/bordeaux?prix_min=800000&prix_max=1400000&surface_min=400'
    ],
    selectors: {
      propertyLinks: 'a[href*="/annonce/"]',
      title: 'h1, .property-title',
      price: '.property-price, .price',
      description: '.property-description, .description',
      area: 'text'
    }
  },

  paruvendu: {
    name: 'ParuVendu',
    baseUrl: 'https://www.paruvendu.fr',
    searchUrls: [
      'https://www.paruvendu.fr/immobilier/annonceimmobiliere/liste/listeAnnonces?tt=1&tbien=7&px1=800000&px2=1400000&rg=69&surface_min=400',
      'https://www.paruvendu.fr/immobilier/annonceimmobiliere/liste/listeAnnonces?tt=1&tbien=7&px1=800000&px2=1400000&rg=33&surface_min=400'
    ],
    selectors: {
      propertyLinks: 'a[href*="/annonce-"]',
      title: 'h1, .annonce-titre',
      price: '.annonce-prix, .prix',
      description: '.annonce-descriptif, .descriptif',
      area: 'text'
    }
  }
};

// Fonction pour faire des requêtes HTTP avec headers réalistes
function fetchPage(url) {
  return new Promise((resolve, reject) => {
    const client = url.startsWith('https') ? https : http;

    const options = {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'fr-FR,fr;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Cache-Control': 'no-cache'
      }
    };

    client.get(url, options, (res) => {
      let data = '';

      // Gérer la compression gzip
      if (res.headers['content-encoding'] === 'gzip') {
        const zlib = require('zlib');
        const gunzip = zlib.createGunzip();
        res.pipe(gunzip);
        gunzip.on('data', chunk => data += chunk);
        gunzip.on('end', () => resolve(data));
        gunzip.on('error', reject);
      } else {
        res.on('data', chunk => data += chunk);
        res.on('end', () => resolve(data));
      }
    }).on('error', reject);
  });
}

// Fonction pour extraire les vraies URLs d'annonces depuis une page de résultats
function extractPropertyUrls(html, siteConfig) {
  const urls = [];
  const baseUrl = siteConfig.baseUrl;

  // Patterns génériques pour trouver les liens d'annonces
  const patterns = [
    new RegExp(siteConfig.selectors.propertyLinks.replace(/[\[\]]/g, '\\$&'), 'gi'),
    /href="([^"]*\/annonce[^"]*)/gi,
    /href="([^"]*\/vente[^"]*)/gi,
    /href="([^"]*\/detail[^"]*)/gi,
    /href="([^"]*\/bien[^"]*)/gi,
    /href="([^"]*\/immobilier[^"]*)/gi
  ];

  for (const pattern of patterns) {
    let match;
    while ((match = pattern.exec(html)) !== null) {
      let url = match[1];

      // Construire l'URL complète
      if (url.startsWith('/')) {
        url = baseUrl + url;
      } else if (!url.startsWith('http')) {
        url = baseUrl + '/' + url;
      }

      // Vérifier que c'est bien une annonce immobilière
      const isPropertyUrl = url.includes('annonce') ||
                           url.includes('vente') ||
                           url.includes('detail') ||
                           url.includes('immobilier') ||
                           url.includes('bien');

      if (isPropertyUrl && !urls.includes(url)) {
        urls.push(url);
      }
    }
  }

  return urls;
}

// Fonction pour extraire les données d'une annonce
function extractPropertyData(html, sourceUrl, siteConfig) {
  // Fonction utilitaire pour extraire du texte
  const extractText = (pattern) => {
    const match = html.match(pattern);
    return match ? match[1].trim() : '';
  };

  // Extraire le titre
  let title = extractText(/<title[^>]*>([^<]+)<\/title>/i) ||
              extractText(/<h1[^>]*>([^<]+)<\/h1>/i) ||
              extractText(/data-qa-id="adview_title"[^>]*>([^<]+)</i) ||
              'Titre non trouvé';

  // Nettoyer le titre
  title = title.replace(/[<>]/g, '').replace(/\s+/g, ' ').trim().substring(0, 100);

  // Extraire le prix
  const pricePatterns = [
    /(\d+(?:\s?\d+)*)\s*€/g,
    /prix[^>]*>([^<]*\d+[^<]*)</gi,
    /montant[^>]*>([^<]*\d+[^<]*)</gi,
    /data-qa-id="adview_price"[^>]*>([^<]*\d+[^<]*)</gi
  ];

  let price = 0;
  for (const pattern of pricePatterns) {
    let match;
    while ((match = pattern.exec(html)) !== null) {
      const priceStr = match[1].replace(/\s/g, '').replace(/[^\d]/g, '');
      const priceNum = parseInt(priceStr);
      if (priceNum >= 100000 && priceNum <= 2000000) { // Prix réaliste
        price = priceNum;
        break;
      }
    }
    if (price > 0) break;
  }

  // Extraire la superficie
  const areaPatterns = [
    /(\d+(?:[,.]?\d+)?)\s*m[²2]/gi,
    /surface[^>]*>([^<]*\d+[^<]*m)/gi,
    /superficie[^>]*>([^<]*\d+[^<]*m)/gi
  ];

  let buildingArea = 0;
  for (const pattern of areaPatterns) {
    let match;
    while ((match = pattern.exec(html)) !== null) {
      const areaStr = match[1].replace(',', '.').replace(/[^\d.]/g, '');
      const areaNum = parseFloat(areaStr);
      if (areaNum >= 100 && areaNum <= 5000) { // Superficie réaliste
        buildingArea = areaNum;
        break;
      }
    }
    if (buildingArea > 0) break;
  }

  // Extraire la description
  const descPatterns = [
    /<meta[^>]*name="description"[^>]*content="([^"]+)"/i,
    /data-qa-id="adview_description_container"[^>]*>([^<]+)</i,
    /<div[^>]*class="[^"]*description[^"]*"[^>]*>([^<]+)</i,
    /<p[^>]*class="[^"]*description[^"]*"[^>]*>([^<]+)</i
  ];

  let description = '';
  for (const pattern of descPatterns) {
    const match = html.match(pattern);
    if (match) {
      description = match[1].trim();
      break;
    }
  }

  // Nettoyer la description
  description = description.replace(/[<>]/g, '').replace(/\s+/g, ' ').trim().substring(0, 300);

  return {
    title,
    description,
    price,
    buildingArea,
    sourceUrl
  };
}

// Fonction pour vérifier si une annonce est disponible
async function checkPropertyAvailability(url) {
  try {
    const html = await fetchPage(url);

    // Vérifier les indicateurs d'indisponibilité
    const unavailableKeywords = [
      'vendu', 'sold', 'retiré', 'indisponible', 'plus disponible',
      'hors marché', 'suspendu', 'réservé', 'en cours de vente',
      '404', 'page non trouvée', 'erreur', 'introuvable'
    ];

    const htmlLower = html.toLowerCase();
    const isUnavailable = unavailableKeywords.some(keyword => htmlLower.includes(keyword));

    return !isUnavailable && html.length > 1000; // Page doit avoir du contenu
  } catch (error) {
    console.log(`⚠️ Erreur vérification ${url}: ${error.message}`);
    return false;
  }
}

// Fonction pour calculer le score de correspondance - FOCUS ENTREPÔTS
function calculateMatchScore(property) {
  let score = 0;

  // Prix (25 points)
  if (property.price >= SEARCH_CRITERIA.minPrice && property.price <= SEARCH_CRITERIA.maxPrice) {
    score += 25;
  } else if (property.price <= SEARCH_CRITERIA.maxPrice * 1.1) {
    score += 15;
  }

  // Superficie bâtiment (20 points)
  if (property.buildingArea >= SEARCH_CRITERIA.minBuildingArea) {
    score += 20;
    if (property.buildingArea >= SEARCH_CRITERIA.idealBuildingArea) {
      score += 5; // Bonus
    }
  } else if (property.buildingArea === 0) {
    score += 10; // Points partiels si superficie non trouvée
  }

  // Ville (15 points)
  if (SEARCH_CRITERIA.cities.some(city => property.city.toLowerCase().includes(city.toLowerCase()))) {
    score += 15;
  }

  // BONUS ENTREPÔTS (40 points) - Le plus important !
  const desc = property.description ? property.description.toLowerCase() : '';
  const title = property.title ? property.title.toLowerCase() : '';
  const fullText = desc + ' ' + title;

  // Mots-clés entrepôts (20 points)
  if (fullText.includes('entrepot') || fullText.includes('entrepôt')) score += 20;
  if (fullText.includes('warehouse') || fullText.includes('hangar')) score += 15;
  if (fullText.includes('local industriel') || fullText.includes('local d\'activité')) score += 15;

  // Caractéristiques industrielles (20 points)
  if (fullText.includes('poids lourd') || fullText.includes('camion')) score += 8;
  if (fullText.includes('quai') || fullText.includes('chargement')) score += 8;
  if (fullText.includes('stockage') || fullText.includes('logistique')) score += 6;
  if (fullText.includes('industriel') || fullText.includes('activité')) score += 6;
  if (fullText.includes('hauteur') || fullText.includes('plafond')) score += 4;
  if (fullText.includes('bureau') && fullText.includes('entrepot')) score += 4; // Bonus si entrepôt + bureaux

  return Math.min(score, 100);
}

// Scraper pour un site spécifique
async function scrapeSite(siteName, siteConfig) {
  console.log(`🔍 Scraping ${siteConfig.name}...`);
  scrapingStatus.currentStep = `Connexion à ${siteConfig.name}...`;

  const properties = [];

  try {
    for (const searchUrl of siteConfig.searchUrls) {
      try {
        const city = searchUrl.includes('lyon') || searchUrl.includes('69') ? 'Lyon' : 'Bordeaux';
        scrapingStatus.currentStep = `Recherche ${city} sur ${siteConfig.name}...`;

        const html = await fetchPage(searchUrl);
        const propertyUrls = extractPropertyUrls(html, siteConfig);

        console.log(`📋 ${propertyUrls.length} URLs trouvées sur ${siteConfig.name} (${city})`);

        // Traiter les premières URLs trouvées
        const urlsToProcess = propertyUrls.slice(0, 3); // Limiter pour éviter la surcharge

        for (let i = 0; i < urlsToProcess.length; i++) {
          const url = urlsToProcess[i];
          scrapingStatus.currentStep = `${siteConfig.name}: Vérification ${i + 1}/${urlsToProcess.length}...`;

          try {
            // Vérifier la disponibilité
            const isAvailable = await checkPropertyAvailability(url);
            if (!isAvailable) {
              console.log(`❌ ${siteConfig.name}: Annonce indisponible: ${url}`);
              continue;
            }

            // Extraire les données
            const html = await fetchPage(url);
            const propertyData = extractPropertyData(html, url, siteConfig);

            console.log(`🔍 ${siteConfig.name}: Données extraites - Prix: ${propertyData.price}€, Surface: ${propertyData.buildingArea}m², Titre: "${propertyData.title}"`);

            // Vérifier si l'annonce correspond aux critères de base (critères assouplis)
            const isValidPrice = propertyData.price >= SEARCH_CRITERIA.minPrice &&
                                 propertyData.price <= SEARCH_CRITERIA.maxPrice;
            const isValidArea = propertyData.buildingArea >= SEARCH_CRITERIA.minBuildingArea ||
                               propertyData.buildingArea === 0; // Accepter si superficie non trouvée
            const isEntrepot = SEARCH_CRITERIA.propertyTypes.some(type =>
              (propertyData.title + ' ' + propertyData.description).toLowerCase().includes(type.toLowerCase())
            );

            console.log(`📊 ${siteConfig.name}: Prix OK: ${isValidPrice}, Surface OK: ${isValidArea}, Entrepôt: ${isEntrepot}`);

            // Accepter si prix OK ET (superficie OK OU c'est un entrepôt)
            if (isValidPrice && (isValidArea || isEntrepot)) {

              const property = {
                id: `${siteName}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
                title: propertyData.title,
                description: propertyData.description,
                price: propertyData.price,
                city: city,
                address: `${city}`,
                buildingArea: propertyData.buildingArea,
                landArea: Math.floor(propertyData.buildingArea * 3.5), // Estimation
                source: siteName,
                sourceUrl: url,
                publishedAt: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000),
                scrapedAt: new Date(),
                isNew: true,
                isAvailable: true,
                matchScore: calculateMatchScore({
                  price: propertyData.price,
                  buildingArea: propertyData.buildingArea,
                  city: city,
                  description: propertyData.description,
                  title: propertyData.title
                })
              };

              properties.push(property);
              console.log(`✅ ${siteConfig.name}: Annonce ajoutée: ${property.title} - ${property.price}€`);
            }

          } catch (error) {
            console.log(`❌ ${siteConfig.name}: Erreur traitement ${url}: ${error.message}`);
          }

          // Pause entre les requêtes
          await new Promise(resolve => setTimeout(resolve, 1500));
        }

      } catch (error) {
        console.log(`❌ ${siteConfig.name}: Erreur recherche: ${error.message}`);
      }
    }

  } catch (error) {
    console.log(`❌ ${siteConfig.name}: Erreur générale: ${error.message}`);
  }

  return properties;
}

// Fonction principale de scraping multi-sites
async function runMultiSiteScraping() {
  if (scrapingStatus.isRunning) {
    console.log('⚠️ Scraping déjà en cours...');
    return { success: false, message: 'Scraping déjà en cours' };
  }

  scrapingStatus.isRunning = true;
  scrapingStatus.currentStep = 'Initialisation...';
  scrapingStatus.progress = 0;

  console.log('🚀 Démarrage du scraping multi-sites...');
  allProperties = []; // Reset

  try {
    const sites = Object.keys(SITES_CONFIG);
    const totalSites = sites.length;

    // Scraper chaque site
    for (let i = 0; i < sites.length; i++) {
      const siteName = sites[i];
      const siteConfig = SITES_CONFIG[siteName];

      scrapingStatus.progress = (i / totalSites) * 100;

      try {
        const siteProperties = await scrapeSite(siteName, siteConfig);
        allProperties.push(...siteProperties);

        console.log(`✅ ${siteConfig.name}: ${siteProperties.length} propriétés ajoutées`);

      } catch (error) {
        console.log(`❌ ${siteConfig.name}: Erreur générale: ${error.message}`);
      }

      // Pause entre les sites
      await new Promise(resolve => setTimeout(resolve, 2000));
    }

    // Filtrer et trier par score (seuil réduit pour plus de résultats)
    allProperties = allProperties
      .filter(prop => prop.matchScore >= 40) // Seuil réduit pour entrepôts
      .sort((a, b) => b.matchScore - a.matchScore);

    // Supprimer les doublons basés sur le titre et le prix
    const uniqueProperties = [];
    const seen = new Set();

    for (const prop of allProperties) {
      const key = `${prop.title}-${prop.price}`;
      if (!seen.has(key)) {
        seen.add(key);
        uniqueProperties.push(prop);
      }
    }

    allProperties = uniqueProperties;

    scrapingStatus.currentStep = 'Terminé !';
    scrapingStatus.progress = 100;

    console.log(`✅ Scraping multi-sites terminé: ${allProperties.length} propriétés uniques trouvées`);

    return {
      success: true,
      propertiesFound: allProperties.length,
      sitesScraped: sites.length,
      timestamp: new Date()
    };

  } catch (error) {
    console.error('❌ Erreur scraping multi-sites:', error);
    return {
      success: false,
      error: error.message,
      timestamp: new Date()
    };
  } finally {
    scrapingStatus.isRunning = false;
    setTimeout(() => {
      scrapingStatus.progress = 0;
    }, 3000);
  }
}

// Fonction utilitaire pour le temps écoulé
function getTimeAgo(date) {
  const diffMs = Date.now() - date;
  const diffMins = Math.floor(diffMs / (1000 * 60));
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));

  if (diffMins < 60) {
    return `il y a ${diffMins} min`;
  } else {
    return `il y a ${diffHours}h`;
  }
}

// Interface web pour afficher les résultats multi-sites
function generateMultiSiteHTML() {
  const stats = {
    total: allProperties.length,
    new: allProperties.filter(p => p.isNew).length,
    avgPrice: allProperties.length > 0 ?
      Math.round(allProperties.reduce((sum, p) => sum + p.price, 0) / allProperties.length) : 0,
    avgScore: allProperties.length > 0 ?
      Math.round(allProperties.reduce((sum, p) => sum + p.matchScore, 0) / allProperties.length) : 0,
    sources: [...new Set(allProperties.map(p => p.source))].length
  };

  const sourceColors = {
    leboncoin: '#f39c12',
    bienici: '#3498db',
    logicimmo: '#e74c3c',
    bureaux_locaux: '#9b59b6',
    paruvendu: '#2ecc71',
    geolocaux: '#34495e',
    seloger: '#e67e22'
  };

  return `
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏭 Industrial Property Scraper - Multi-Sites</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc; color: #334155; line-height: 1.6;
        }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white; padding: 30px; text-align: center; margin-bottom: 30px; border-radius: 12px;
        }
        .multi-indicator {
            display: inline-flex; align-items: center; gap: 8px; margin-top: 10px;
            background: rgba(255,255,255,0.2); padding: 8px 16px; border-radius: 20px;
        }
        .multi-dot {
            width: 8px; height: 8px; background: #22c55e; border-radius: 50%;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        .stats {
            display: grid; grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 20px; margin-bottom: 30px;
        }
        .stat-card {
            background: white; padding: 20px; border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center;
        }
        .stat-number { font-size: 2rem; font-weight: bold; color: #667eea; }
        .property-card {
            background: white; border-radius: 12px; padding: 24px; margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1); transition: transform 0.2s;
            border-left: 4px solid #667eea;
        }
        .property-card:hover { transform: translateY(-2px); box-shadow: 0 8px 25px rgba(0,0,0,0.15); }
        .property-header { display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 15px; }
        .property-title { font-size: 1.25rem; font-weight: 600; color: #1e293b; margin-bottom: 5px; }
        .property-price { font-size: 1.5rem; font-weight: bold; color: #667eea; }
        .badges { display: flex; gap: 8px; margin-bottom: 10px; flex-wrap: wrap; }
        .badge {
            padding: 4px 12px; border-radius: 20px; font-size: 0.75rem; font-weight: 500;
        }
        .badge-success { background: #dcfce7; color: #166534; }
        .badge-multi { background: #fef3c7; color: #92400e; }
        .badge-verified { background: #dcfce7; color: #166534; }
        .property-details {
            display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px; padding-top: 15px; border-top: 1px solid #e2e8f0;
        }
        .detail-item { display: flex; align-items: center; gap: 8px; font-size: 0.9rem; }
        .btn {
            padding: 12px 24px; border: none; border-radius: 6px; font-weight: 500;
            cursor: pointer; transition: all 0.2s; margin: 5px; text-decoration: none;
            display: inline-block;
        }
        .btn-primary { background: #667eea; color: white; }
        .btn-primary:hover { background: #5a67d8; }
        .btn-secondary { background: #f1f5f9; color: #475569; }
        .btn-success { background: #22c55e; color: white; }
        .btn-success:hover { background: #16a34a; }
        .scraping-control {
            background: white; padding: 25px; border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1); margin-bottom: 30px; text-align: center;
        }
        .progress-section {
            background: white; padding: 25px; border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1); margin-bottom: 30px;
            display: ${scrapingStatus.isRunning ? 'block' : 'none'};
        }
        .progress-bar {
            width: 100%; height: 8px; background: #e2e8f0; border-radius: 4px;
            overflow: hidden; margin: 15px 0;
        }
        .progress-fill {
            height: 100%; background: #22c55e; width: ${scrapingStatus.progress}%;
            transition: width 0.3s ease;
        }
        .sites-list {
            background: white; padding: 20px; border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1); margin-bottom: 30px;
        }
        .site-item {
            display: inline-block; margin: 5px; padding: 8px 16px;
            background: #f1f5f9; border-radius: 20px; font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏭 Industrial Property Scraper</h1>
            <p>Agent Multi-Sites - Tous les sites immobiliers</p>
            <div class="multi-indicator">
                <div class="multi-dot"></div>
                <span>MULTI-SITES - ${Object.keys(SITES_CONFIG).length} sources</span>
            </div>
        </div>

        <div class="sites-list">
            <h3 style="margin-bottom: 15px;">🌐 Sites scrapés</h3>
            ${Object.entries(SITES_CONFIG).map(([key, config]) => `
                <span class="site-item" style="background-color: ${sourceColors[key] || '#f1f5f9'}20; color: ${sourceColors[key] || '#64748b'};">
                    ${config.name}
                </span>
            `).join('')}
        </div>

        <div class="scraping-control">
            <h3>🔄 Scraping Multi-Sites</h3>
            <p style="margin: 15px 0; color: #64748b;">
                L'agent va scraper ${Object.keys(SITES_CONFIG).length} sites immobiliers pour trouver toutes les annonces disponibles
            </p>
            <button onclick="startMultiSiteScraping()" class="btn btn-success" ${scrapingStatus.isRunning ? 'disabled' : ''}>
                ${scrapingStatus.isRunning ? '⏳ Scraping en cours...' : '🚀 Scraper tous les sites'}
            </button>
            <div id="status" style="margin-top: 15px; color: #667eea;">
                ✅ Dernière vérification: ${new Date().toLocaleTimeString('fr-FR')}
            </div>
        </div>

        ${scrapingStatus.isRunning ? `
        <div class="progress-section">
            <h3>Scraping multi-sites en cours...</h3>
            <div id="currentStep">${scrapingStatus.currentStep}</div>
            <div class="progress-bar">
                <div class="progress-fill"></div>
            </div>
            <div style="color: #64748b; font-size: 0.9rem;">
                Recherche sur tous les sites immobiliers...
            </div>
        </div>
        ` : ''}

        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">${stats.total}</div>
                <div class="stat-label">Annonces trouvées</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${stats.sources}</div>
                <div class="stat-label">Sources actives</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${stats.avgPrice.toLocaleString('fr-FR')}€</div>
                <div class="stat-label">Prix moyen</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${stats.avgScore}%</div>
                <div class="stat-label">Score moyen</div>
            </div>
        </div>

        <div class="properties">
            ${allProperties.map(property => `
                <div class="property-card">
                    <div class="property-header">
                        <div>
                            <div class="property-title">${property.title}</div>
                            <div class="badges">
                                <span class="badge" style="background-color: ${sourceColors[property.source] || '#f1f5f9'}20; color: ${sourceColors[property.source] || '#64748b'};">
                                    ${SITES_CONFIG[property.source]?.name || property.source}
                                </span>
                                <span class="badge badge-success">${property.matchScore}% match</span>
                                <span class="badge badge-verified">✅ Vérifiée</span>
                                ${property.isNew ? '<span class="badge badge-multi">🆕 Nouvelle</span>' : ''}
                            </div>
                        </div>
                        <div class="property-price">${property.price.toLocaleString('fr-FR')} €</div>
                    </div>

                    <div style="color: #64748b; margin-bottom: 15px;">
                        ${property.description}
                    </div>

                    <div class="property-details">
                        <div class="detail-item">
                            <span>📍</span>
                            <span>${property.address}</span>
                        </div>
                        <div class="detail-item">
                            <span>📐</span>
                            <span>${property.buildingArea}m² bâtiment</span>
                        </div>
                        <div class="detail-item">
                            <span>🏞️</span>
                            <span>${property.landArea}m² terrain</span>
                        </div>
                        <div class="detail-item">
                            <span>🔍</span>
                            <span>Scrapé ${getTimeAgo(property.scrapedAt)}</span>
                        </div>
                    </div>

                    <div style="margin-top: 20px; text-align: center;">
                        <a href="${property.sourceUrl}" target="_blank" class="btn btn-primary">
                            🔗 Voir l'annonce
                        </a>
                        <button class="btn btn-secondary" onclick="alert('Propriété sauvegardée!')">
                            💾 Sauvegarder
                        </button>
                    </div>
                </div>
            `).join('')}
        </div>

        ${allProperties.length === 0 ? `
            <div style="text-align: center; padding: 40px; background: white; border-radius: 12px;">
                <h3>🔍 ${scrapingStatus.isRunning ? 'Scraping en cours...' : 'Prêt à scraper tous les sites'}</h3>
                <p style="color: #64748b; margin: 15px 0;">
                    ${scrapingStatus.isRunning ?
                      'L\'agent recherche sur tous les sites immobiliers...' :
                      'Cliquez pour lancer la recherche sur tous les sites immobiliers.'
                    }
                </p>
                ${!scrapingStatus.isRunning ? `
                <button onclick="startMultiSiteScraping()" class="btn btn-success">
                    🚀 Scraper tous les sites
                </button>
                ` : ''}
            </div>
        ` : ''}
    </div>

    <script>
        async function startMultiSiteScraping() {
            const status = document.getElementById('status');
            status.innerHTML = '🔄 Lancement du scraping multi-sites...';
            status.style.color = '#f59e0b';

            try {
                const response = await fetch('/api/scraping/multi-sites', { method: 'POST' });
                const result = await response.json();

                if (result.success) {
                    status.innerHTML = '✅ Scraping terminé - Rechargement...';
                    status.style.color = '#22c55e';
                    setTimeout(() => window.location.reload(), 2000);
                } else {
                    status.innerHTML = '❌ Erreur lors du scraping: ' + (result.error || result.message);
                    status.style.color = '#dc2626';
                }
            } catch (error) {
                status.innerHTML = '❌ Erreur de connexion';
                status.style.color = '#dc2626';
            }
        }

        // Auto-refresh pendant le scraping
        ${scrapingStatus.isRunning ? `
        setInterval(() => {
            if (window.location.pathname === '/') {
                window.location.reload();
            }
        }, 10000);
        ` : ''}
    </script>
</body>
</html>
  `;
}

// Serveur HTTP
const server = http.createServer(async (req, res) => {
  const url = new URL(req.url, `http://${req.headers.host}`);

  if (url.pathname === '/') {
    res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
    res.end(generateMultiSiteHTML());
  } else if (url.pathname === '/api/scraping/multi-sites' && req.method === 'POST') {
    const result = await runMultiSiteScraping();
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify(result));
  } else if (url.pathname === '/api/status') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      isRunning: scrapingStatus.isRunning,
      currentStep: scrapingStatus.currentStep,
      progress: scrapingStatus.progress,
      propertiesCount: allProperties.length,
      sitesCount: Object.keys(SITES_CONFIG).length
    }));
  } else {
    res.writeHead(404, { 'Content-Type': 'text/plain' });
    res.end('Page non trouvée');
  }
});

// Démarrage
server.listen(PORT, () => {
  console.log(`🚀 Agent de scraping MULTI-SITES démarré sur http://localhost:${PORT}`);
  console.log(`🌐 ${Object.keys(SITES_CONFIG).length} sites configurés: ${Object.values(SITES_CONFIG).map(s => s.name).join(', ')}`);
  console.log('✅ Prêt ! Ouvrez http://localhost:3000 pour scraper tous les sites');
});
